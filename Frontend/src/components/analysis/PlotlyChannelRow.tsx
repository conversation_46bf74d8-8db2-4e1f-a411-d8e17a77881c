'use client';

import React, { useMemo } from 'react';
import Plot from 'react-plotly.js';
import type { HFOEvent } from '@/types/hfo';
import { HFO_TYPE_COLORS } from '@/types/hfo';
import type { PlotlyTrace } from '@/types/plotly';

interface PlotlyChannelRowProps {
  channelName: string;
  data: number[];
  timeWindow: [number, number];
  samplingRate: number;
  height?: number;
  hfoEvents?: HFOEvent[];
  showHFOMarkers?: boolean;
  isEven?: boolean;
}

export const PlotlyChannelRow: React.FC<PlotlyChannelRowProps> = ({
  channelName,
  data,
  timeWindow,
  samplingRate,
  height = 80,
  hfoEvents = [],
  showHFOMarkers = true,
  isEven = true,
}) => {
  const plotData = useMemo(() => {
    if (!data || data.length === 0) {
      return [];
    }

    const timeAxis = data.map((_, i) => i / samplingRate);

    const startIdx = Math.floor(timeWindow[0] * samplingRate);
    const endIdx = Math.floor(timeWindow[1] * samplingRate);
    const windowedTime = timeAxis.slice(startIdx, endIdx);
    const windowedData = data.slice(startIdx, endIdx);

    const traces: PlotlyTrace[] = [{
      x: windowedTime,
      y: windowedData,
      type: 'scatter',
      mode: 'lines',
      name: channelName,
      line: { color: '#000000', width: 0.8 },
      hovertemplate: `Time: %{x:.2f}s<br>Amplitude: %{y:.2f}μV<extra></extra>`,
    }];

    return traces;
  }, [data, channelName, timeWindow, samplingRate]);

  const layout = useMemo(() => {
    // Create shapes for HFO markers
    const shapes: Partial<Plotly.Shape>[] = [];

    if (showHFOMarkers && hfoEvents.length > 0) {
      hfoEvents.forEach(event => {
        const color = HFO_TYPE_COLORS[event.type || 'accepted'];

        // Add vertical line at HFO start
        shapes.push({
          type: 'line',
          x0: event.start_time,
          x1: event.start_time,
          y0: 0,
          y1: 1,
          xref: 'x',
          yref: 'paper',
          line: {
            color: color,
            width: 1.5,
            dash: event.type === 'rejected_long' ? 'dot' : 'solid'
          },
          opacity: 0.8
        });

        // Add shaded region for HFO duration
        if (event.end_time && event.end_time - event.start_time > 0.001) {
          shapes.push({
            type: 'rect',
            x0: event.start_time,
            x1: event.end_time,
            y0: 0,
            y1: 1,
            xref: 'x',
            yref: 'paper',
            fillcolor: color,
            opacity: 0.1,
            line: {
              width: 0
            }
          });
        }
      });
    }

    // Create annotations for HFO counts
    const annotations: Partial<Plotly.Annotations>[] = [{
      x: 0,
      y: 0.5,
      xref: 'paper' as const,
      yref: 'paper' as const,
      text: channelName,
      showarrow: false,
      xanchor: 'right' as const,
      xshift: -10,
      font: {
        size: 11,
        color: '#4a5568',
        family: 'var(--font-sans), system-ui, -apple-system, sans-serif',
      },
    }];

    // Add HFO count annotation
    if (hfoEvents.length > 0) {
      const accepted = hfoEvents.filter(e => (e.type || 'accepted') === 'accepted').length;
      const rejected = hfoEvents.filter(e => e.type && e.type !== 'accepted').length;

      let countText = '';
      if (accepted > 0) countText += `<span style="color:${HFO_TYPE_COLORS.accepted}">${accepted}✓</span> `;
      if (rejected > 0) countText += `<span style="color:${HFO_TYPE_COLORS.rejected}">${rejected}✗</span>`;

      if (countText) {
        annotations.push({
          x: 1,
          y: 0.9,
          xref: 'paper' as const,
          yref: 'paper' as const,
          text: countText,
          showarrow: false,
          xanchor: 'right' as const,
          font: {
            size: 10,
            family: 'var(--font-sans), system-ui, -apple-system, sans-serif',
          },
        });
      }
    }

    return {
      xaxis: {
        range: timeWindow,
        showgrid: false,
        showticklabels: false,
        zeroline: false,
        showline: false,
      },
      yaxis: {
        showgrid: false,
        showticklabels: false,
        zeroline: false,
        showline: false,
        autorange: true,
      },
      height: height,
      margin: { l: 80, r: 60, t: 5, b: 5 },
      hovermode: 'x' as const,
      showlegend: false,
      plot_bgcolor: isEven ? '#fafafa' : '#ffffff',
      paper_bgcolor: isEven ? '#fafafa' : '#ffffff',
      shapes: shapes,
      annotations: annotations,
    };
  }, [channelName, timeWindow, height, showHFOMarkers, hfoEvents, isEven]);

  const config = {
    displayModeBar: false,
    responsive: true,
    staticPlot: false,
  };

  return (
    <div className="border-b border-gray-100 hover:bg-gray-50 transition-colors">
      <Plot
        data={plotData}
        layout={layout}
        config={config}
        style={{ width: '100%', height: `${height}px` }}
      />
    </div>
  );
};

export default React.memo(PlotlyChannelRow);